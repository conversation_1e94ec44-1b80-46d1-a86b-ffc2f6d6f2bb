# LXD容器流量管理功能

## 功能概述

这个流量管理功能为ZJMF-LXD-Server添加了类似产品生命周期的流量重置机制：

- **周期性重置**：基于容器开通日期，每月自动重置流量统计
- **手动重置**：支持通过API或命令行手动重置容器流量
- **历史记录**：记录每次流量重置的详细历史
- **自动管理**：新容器自动注册，删除时自动注销

## 工作原理

### 流量统计方式
- **传统方式**：直接使用LXD容器的累计流量统计
- **新方式**：记录基准值，计算当前周期内的实际使用量

```
当前周期使用量 = 当前总流量 - 基准流量
```

### 重置机制
1. **容器创建时**：记录开通日期，设置下次重置日期
2. **每月重置**：在开通日期的每月对应日自动重置
3. **重置操作**：更新基准值，重新计算下次重置日期

## 安装步骤

### 1. 运行安装脚本
```bash
cd /path/to/zjmf-lxd-server/server
sudo bash setup_flow_management.sh
```

### 2. 验证安装
```bash
# 检查定时任务
crontab -l | grep flow_reset

# 检查管理命令
lxd-flow-manager

# 测试API
curl -H "apikey: YOUR_TOKEN" \
     "http://localhost:8060/api/flow/info?hostname=test-container"
```

## 使用方法

### 命令行管理

```bash
# 查看容器流量信息
lxd-flow-manager info container-name

# 手动重置容器流量
lxd-flow-manager reset container-name

# 执行自动流量重置（检查所有到期容器）
lxd-flow-manager auto-reset

# 注册现有容器到流量管理系统
lxd-flow-manager register
```

### API接口

#### 重置容器流量
```bash
curl -X POST \
     -H "apikey: YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"hostname": "container-name"}' \
     http://localhost:8060/api/flow/reset
```

#### 获取流量管理信息
```bash
curl -H "apikey: YOUR_TOKEN" \
     "http://localhost:8060/api/flow/info?hostname=container-name"
```

### 响应示例

#### 流量信息响应
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "hostname": "test-container",
    "created_date": "2025-08-01",
    "last_reset_date": "2025-08-01",
    "next_reset_date": "2025-09-01",
    "flow_limit_gb": 100,
    "baseline_bytes_received": 1048576,
    "baseline_bytes_sent": 524288,
    "current_period_start": "2025-08-01",
    "reset_count": 1,
    "auto_reset_enabled": 1,
    "reset_history": [
      {
        "reset_date": "2025-08-01",
        "reset_type": "manual",
        "flow_used_gb": 0.5
      }
    ]
  }
}
```

## 数据库结构

### 容器流量管理表 (container_flow_management)
- `hostname`: 容器名称
- `created_date`: 容器开通日期
- `next_reset_date`: 下次重置日期
- `flow_limit_gb`: 流量限制(GB)
- `baseline_bytes_*`: 基准流量值
- `reset_count`: 重置次数

### 流量重置历史表 (flow_reset_history)
- `hostname`: 容器名称
- `reset_date`: 重置日期
- `reset_type`: 重置类型(auto/manual)
- `flow_used_gb`: 重置前使用量

## 定时任务

系统会自动添加以下定时任务：

```bash
# 每天凌晨2点检查并重置到期流量
0 2 * * * /usr/bin/python3 /path/to/flow_reset_scheduler.py reset
```

## 注意事项

### 重要提醒
1. **数据备份**：建议定期备份流量管理数据库
2. **时区设置**：确保服务器时区设置正确
3. **权限要求**：定时任务需要适当的权限访问LXD

### 兼容性
- **现有容器**：安装后需要运行注册命令
- **新容器**：自动注册到流量管理系统
- **删除容器**：自动从系统中注销

### 故障排除

#### 常见问题
1. **定时任务不执行**
   ```bash
   # 检查cron服务状态
   systemctl status cron
   
   # 查看cron日志
   tail -f /var/log/cron
   ```

2. **数据库权限问题**
   ```bash
   # 检查数据库文件权限
   ls -la flow_management.db
   
   # 修复权限
   chown lxd:lxd flow_management.db
   ```

3. **API调用失败**
   ```bash
   # 检查服务状态
   systemctl status your-lxd-service
   
   # 查看应用日志
   tail -f flow_reset.log
   ```

## 升级和维护

### 升级流程
1. 备份现有数据库
2. 更新代码文件
3. 运行安装脚本
4. 验证功能正常

### 维护建议
- 定期检查日志文件大小
- 清理过期的历史记录
- 监控定时任务执行情况

## 技术支持

如有问题，请检查：
1. 系统日志：`/var/log/syslog`
2. 应用日志：`flow_reset.log`
3. LXD日志：`journalctl -u lxd`
