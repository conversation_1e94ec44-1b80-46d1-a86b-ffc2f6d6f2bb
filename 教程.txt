# ZJMF-LXD-Server 完整部署教程

## 1. 系统环境准备

### 1.1 基础软件安装
```bash
apt update -y
apt install wget curl sudo git screen nano unzip iptables-persistent iptables -y
# 中途v4和v6规则都选NO
apt install python3-pip python3 sqlite3 cron -y
rm /usr/lib/python3.11/EXTERNALLY-MANAGED
```

### 1.2 Python依赖安装
```bash
pip3 install Flask pylxd python-dateutil configparser
```

## 2. LXD环境配置

### 2.1 安装LXD
```bash
bash <(curl -s https://raw.githubusercontent.com/jiayihello/LinuxTools/main/linuxtools.sh)
```
安装过程：1 ) LXD 安装与镜像管理 → 1) 安装或检查 LXD 环境 → 初始化配置

### 2.2 创建BTRFS存储池
```bash
bash <(curl -s https://raw.githubusercontent.com/jiayihello/LinuxTools/main/linuxtools.sh)
```
创建过程：1 ) LXD 安装与镜像管理 → 5) 管理 BTRFS 存储池 → 1) 创建新的 LXD BTRFS 存储池 → 1) 从镜像文件创建 → 设置大小并且设置默认存储

### 2.3 下载系统镜像
```bash
bash <(curl -s https://raw.githubusercontent.com/jiayihello/LinuxTools/main/linuxtools.sh)
```
创建过程：1 ) LXD 安装与镜像管理 → 2) 下载镜像

## 3. 后端服务部署

### 3.1 项目文件部署
```bash
# 复制 zjmf-lxd-server-main/server 目录到 /root/server
cp -r zjmf-lxd-server-main/server /root/
cd /root/server

# 安装Python依赖
pip3 install -r requirements.txt
```

### 3.2 配置文件设置
在 server 目录下，修改 app.ini 配置文件：

```ini
[server]
HTTP_PORT = 8060
TOKEN = 你的安全密钥
LOG_LEVEL = INFO

[lxc]
DEFAULT_IMAGE_ALIAS = debian12
NETWORK_BRIDGE = lxdbr0
STORAGE_POOL = disk
DEFAULT_CONTAINER_USER = root
MAIN_INTERFACE = ens192
NAT_LISTEN_IP = 你的服务器IP
```

### 3.3 网络环境配置
```bash
# 配置网络规则和iptables
chmod +x network_setup.py
python3 network_setup.py

# 或者运行网络配置脚本
bash setup_network_service.sh
```

## 4. 流量管理功能部署

### 4.1 安装流量管理功能

#### 方式一：标准安装（推荐新手）
```bash
cd /root/server
chmod +x setup_flow_management.sh
sudo bash setup_flow_management.sh
```

#### 方式二：生产环境部署（推荐生产使用）
```bash
cd /root/server
chmod +x deploy_production_flow.sh
sudo bash deploy_production_flow.sh
```

**两种方式的区别：**
- **标准安装**: 快速部署，固定配置，适合测试和小规模使用
- **生产部署**: 智能检测环境，性能优化，完整监控，适合生产环境

**生产部署的额外功能：**
- 🔍 自动检测容器数量并优化配置
- 📊 完整的监控和日志管理系统
- 🛡️ 资源限制和故障恢复机制
- 📈 性能优化和定时监控

### 4.2 验证流量管理安装
```bash
# 检查定时任务
crontab -l | grep flow_reset

# 检查管理命令
lxd-flow-manager

# 检查守护进程状态
systemctl status lxd-flow-continuity.service
```

## 5. 系统服务配置

### 5.1 创建主服务
创建文件 /etc/systemd/system/lxd-api.service：

```ini
[Unit]
Description=LXD Management API Service
After=network.target lxd.service
Wants=lxd.service

[Service]
User=root
Group=root
WorkingDirectory=/root/server
ExecStart=/usr/bin/python3 app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 5.2 启动服务
```bash
systemctl daemon-reload
systemctl enable lxd-api.service
systemctl start lxd-api.service
systemctl status lxd-api.service
```

## 6. 功能验证与测试

### 6.1 API接口测试
```bash
# 测试基础API连接
curl -H "apikey: 你的TOKEN" "http://localhost:8060/api/check"
# 应该返回: {"code":200,"msg":"API连接正常"}

# 测试容器创建API
curl -X POST \
     -H "apikey: 你的TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "hostname": "test-container",
       "image": "debian12",
       "cpu": 1,
       "memory": 512,
       "disk": 1024,
       "bandwidth": 100
     }' \
     http://localhost:8060/api/create

# 测试获取容器信息（需要先创建容器）
curl -H "apikey: 你的TOKEN" "http://localhost:8060/api/getinfo?hostname=test-container"
```

### 6.2 流量管理功能测试
```bash
# 查看容器流量信息
lxd-flow-manager info test-container

# 手动重置容器流量
lxd-flow-manager reset test-container

# 测试流量管理API
curl -H "apikey: 你的TOKEN" \
     "http://localhost:8060/api/flow/info?hostname=test-container"
```

### 6.3 Web界面访问
访问 http://你的服务器IP:8060 进入Web管理界面
默认密码为你在app.ini中设置的TOKEN

## 7. 流量管理功能详解

### 7.1 功能特性
- **周期性重置**：基于容器开通日期，每月自动重置流量统计
- **手动重置**：支持通过API或命令行手动重置容器流量
- **历史记录**：记录每次流量重置的详细历史
- **自动管理**：新容器自动注册，删除时自动注销
- **重启连续性**：容器重启后流量统计保持连续

### 7.2 工作原理
流量统计采用基准值方式：
```
当前周期使用量 = 当前总流量 - 基准流量
```

### 7.3 管理命令
```bash
# 查看容器流量信息
lxd-flow-manager info <容器名>

# 手动重置容器流量
lxd-flow-manager reset <容器名>

# 执行自动流量重置（检查所有到期容器）
lxd-flow-manager auto-reset

# 注册现有容器到流量管理系统
lxd-flow-manager register

# 清理已删除容器的记录
lxd-flow-manager cleanup

# 查看守护进程状态
lxd-flow-manager daemon-status

# 重启守护进程
lxd-flow-manager daemon-restart
```

## 8. 系统监控与维护

### 8.1 服务状态检查
```bash
# 检查主服务状态
systemctl status lxd-api.service

# 检查流量管理守护进程
systemctl status lxd-flow-continuity.service

# 查看服务日志
journalctl -u lxd-api.service -f
journalctl -u lxd-flow-continuity.service -f
```

### 8.2 定时任务
系统自动添加的定时任务：
```bash
# 每天凌晨2点检查并重置到期流量
0 2 * * * /usr/bin/python3 /root/server/flow_reset_scheduler.py reset

# 每周日凌晨3点清理已删除容器记录
0 3 * * 0 /usr/bin/python3 /root/server/flow_reset_scheduler.py cleanup
```

### 8.3 数据备份
```bash
# 手动备份流量管理数据库
cp /root/server/flow_management.db /root/server/flow_management.db.backup.$(date +%Y%m%d)

# 设置自动备份（添加到crontab）
0 1 * * * cp /root/server/flow_management.db /root/server/flow_management.db.backup.$(date +\%Y\%m\%d)

# 清理旧备份
0 2 * * 0 find /root/server -name "*.backup.*" -mtime +7 -delete
```

## 9. 常见问题与故障排除

### 9.1 LXD相关问题
```bash
# LXD服务未启动
sudo systemctl start lxd
sudo systemctl enable lxd

# LXD初始化问题
sudo lxd init --auto

# 检查LXD状态
lxc list
lxd --version
```

### 9.2 网络连接问题
```bash
# 检查网络桥接
ip link show lxdbr0

# 重新配置网络
python3 /root/server/network_setup.py

# 检查iptables规则
iptables -L -n
iptables -t nat -L -n
```

### 9.3 流量管理问题
```bash
# 检查数据库
sqlite3 /root/server/flow_management.db ".tables"

# 重新初始化流量管理
python3 /root/server/upgrade_flow_database.py

# 检查定时任务
crontab -l | grep flow

# 手动运行流量重置
python3 /root/server/flow_reset_scheduler.py reset
```

### 9.4 服务启动问题
```bash
# 检查端口占用
netstat -tlnp | grep 8060

# 检查配置文件
cat /root/server/app.ini

# 查看详细错误日志
journalctl -u lxd-api.service --no-pager -l
```

## 10. 安全配置建议

### 10.1 更改默认TOKEN
```bash
# 生成安全的TOKEN
TOKEN=$(openssl rand -hex 32)
echo "新TOKEN: $TOKEN"

# 更新配置文件
sed -i "s/TOKEN = .*/TOKEN = $TOKEN/" /root/server/app.ini

# 重启服务
systemctl restart lxd-api.service
```

### 10.2 防火墙配置
```bash
# 开放HTTP端口（仅限内网）
ufw allow from ***********/16 to any port 8060
ufw allow from 10.0.0.0/8 to any port 8060

# 或者使用iptables
iptables -A INPUT -s ***********/16 -p tcp --dport 8060 -j ACCEPT
iptables -A INPUT -p tcp --dport 8060 -j DROP
```

### 10.3 SSL/TLS配置（可选）
```bash
# 安装nginx作为反向代理
apt install nginx

# 配置SSL证书
# 创建nginx配置文件 /etc/nginx/sites-available/lxd-api
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;

    location / {
        proxy_pass http://127.0.0.1:8060;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 11. API接口文档

### 11.1 容器管理API
```bash
# API连接检查
GET /api/check
Headers: apikey: YOUR_TOKEN

# 获取单个容器信息
GET /api/getinfo?hostname=容器名
Headers: apikey: YOUR_TOKEN

# 创建容器
POST /api/create
Headers: apikey: YOUR_TOKEN, Content-Type: application/json
Body: {
  "hostname": "container-name",
  "image": "debian12",
  "cpu": 2,
  "memory": 1024,
  "disk": 2048,
  "bandwidth": 100
}

# 删除容器
POST /api/delete
Headers: apikey: YOUR_TOKEN, Content-Type: application/json
Body: {"hostname": "container-name"}

# 启动容器
POST /api/start
Headers: apikey: YOUR_TOKEN, Content-Type: application/json
Body: {"hostname": "container-name"}

# 停止容器
POST /api/stop
Headers: apikey: YOUR_TOKEN, Content-Type: application/json
Body: {"hostname": "container-name"}

# 重启容器
POST /api/restart
Headers: apikey: YOUR_TOKEN, Content-Type: application/json
Body: {"hostname": "container-name"}
```

### 11.2 流量管理API
```bash
# 获取流量信息
GET /api/flow/info?hostname=container-name
Headers: apikey: YOUR_TOKEN

# 重置容器流量
POST /api/flow/reset
Headers: apikey: YOUR_TOKEN, Content-Type: application/json
Body: {"hostname": "container-name"}
```

## 12. 高级配置

### 12.1 性能优化
```bash
# 根据服务器规模调整配置
# 编辑 /root/server/production_flow_config.py

# 小规模环境（<10个容器）
FLOW_DAEMON_INTERVAL = 180  # 3分钟检查一次

# 中等规模环境（10-50个容器）
FLOW_DAEMON_INTERVAL = 300  # 5分钟检查一次

# 大规模环境（>50个容器）
FLOW_DAEMON_INTERVAL = 600  # 10分钟检查一次

# 重启守护进程应用配置
systemctl restart lxd-flow-continuity.service
```

### 12.2 日志管理
```bash
# 创建日志轮转配置
cat > /etc/logrotate.d/lxd-flow-management << 'EOF'
/root/server/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload lxd-api.service
    endscript
}
EOF

# 手动轮转日志
logrotate -f /etc/logrotate.d/lxd-flow-management
```

### 12.3 监控脚本
```bash
# 创建系统监控脚本
cat > /usr/local/bin/lxd-flow-monitor << 'EOF'
#!/bin/bash
echo "=== LXD-API 系统状态监控 ==="
echo "时间: $(date)"
echo ""

echo "=== 服务状态 ==="
systemctl is-active lxd-api.service && echo "✅ 主服务运行正常" || echo "❌ 主服务异常"
systemctl is-active lxd-flow-continuity.service && echo "✅ 流量守护进程运行正常" || echo "❌ 流量守护进程异常"
systemctl is-active lxd.service && echo "✅ LXD服务运行正常" || echo "❌ LXD服务异常"

echo ""
echo "=== 容器状态 ==="
lxc list --format csv | wc -l | xargs echo "容器总数:"
lxc list --format csv | grep RUNNING | wc -l | xargs echo "运行中容器:"

echo ""
echo "=== 系统资源 ==="
echo "内存使用: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "磁盘使用: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5")"}')"

echo ""
echo "=== 网络状态 ==="
ip link show lxdbr0 >/dev/null 2>&1 && echo "✅ LXD网络桥接正常" || echo "❌ LXD网络桥接异常"

echo ""
echo "=== 最近错误日志 ==="
journalctl -u lxd-api.service --since "1 hour ago" --no-pager | grep -i error | tail -3
EOF

chmod +x /usr/local/bin/lxd-flow-monitor
```

## 13. 魔方财务对接

### 13.1 lxdserver.php配置
将 zjmf-lxd-server-main/lxdserver/lxdserver.php 文件放置到魔方财务的模块目录中。

### 13.2 魔方财务配置
在魔方财务后台配置LXD服务器：
- 服务器地址：http://你的服务器IP:8060
- API密钥：你在app.ini中设置的TOKEN
- 确保网络连通性

## 14. 部署完成检查清单

- [ ] 系统依赖安装完成
- [ ] LXD环境配置正确
- [ ] Python依赖安装成功
- [ ] 项目代码部署完成
- [ ] 配置文件设置正确
- [ ] 网络规则配置成功
- [ ] 主服务运行正常
- [ ] 流量管理功能正常
- [ ] 流量守护进程运行正常
- [ ] 定时任务设置成功
- [ ] API接口测试通过
- [ ] 流量重置功能正常
- [ ] Web界面访问正常
- [ ] 安全配置完成
- [ ] 监控脚本运行正常
- [ ] 日志轮转配置完成
- [ ] 备份策略配置

## 15. 总结

本教程涵盖了ZJMF-LXD-Server的完整部署流程，包括：

### 核心功能
- ✅ LXD容器生命周期管理
- ✅ 网络端口映射和NAT配置
- ✅ 流量统计和监控
- ✅ 每月自动流量重置
- ✅ 容器重启后流量连续性
- ✅ 手动流量重置功能
- ✅ 完整的REST API接口
- ✅ Web管理界面
- ✅ 魔方财务系统对接

### 流量管理特色
- **产品级流量重置**：基于容器开通日期的月度重置
- **重启连续性**：容器重启后流量统计不丢失
- **历史记录**：完整的流量重置历史追踪
- **自动化管理**：新容器自动注册，删除时自动清理

### 生产级特性
- **系统服务**：完整的systemd服务配置
- **定时任务**：自动化的流量重置和清理
- **监控告警**：系统状态监控脚本
- **日志管理**：日志轮转和归档
- **安全配置**：API密钥认证和访问控制

系统现在已经准备好为您提供稳定可靠的LXD容器管理服务！

如有问题，请检查：
1. 系统日志：journalctl -u lxd-api.service -f
2. 流量管理日志：journalctl -u lxd-flow-continuity.service -f
3. LXD日志：journalctl -u lxd -f
4. 运行监控脚本：lxd-flow-monitor
