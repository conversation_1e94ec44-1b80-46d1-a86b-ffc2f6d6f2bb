apt update -y
apt install wget curl sudo git screen nano unzip iptables-persistent iptables -y
中途v4和v6规则都选NO
apt install python3-pip python3 -y
rm /usr/lib/python3.11/EXTERNALLY-MANAGED
2. LXD安装
1.安装LXD
bash <(curl -s https://raw.githubusercontent.com/jiayihello/LinuxTools/main/linuxtools.sh)
安装过程1 ) LXD 安装与镜像管理 → 1) 安装或检查 LXD 环境 → 初始化配置
2.创建BTRFS储存
bash <(curl -s https://raw.githubusercontent.com/jiayihello/LinuxTools/main/linuxtools.sh)
创建过程1 ) LXD 安装与镜像管理 → 5) 管理 BTRFS 存储池 → 1) 创建新的 LXD BTRFS 存储池 → 1) 从镜像文件创建 → 设置大小并且设置默认储存
3.镜像下载
bash <(curl -s https://raw.githubusercontent.com/jiayihello/LinuxTools/main/linuxtools.sh)
创建过程1 ) LXD 安装与镜像管理 → 2) 下载镜像
4.后端安装
复制 server目录到root下
 进入server目录，安装依赖
cd zjmf-lxd-server/server
pip3 install -r requirements.txt
配置 app.ini
在 server 目录下，修改一个名为 app.ini 的配置文件。文件内容如下：

[server]
HTTP_PORT = 8080
TOKEN = 7215EE9C7D9DC229D2921A40E899EC5F
LOG_LEVEL = INFO

[lxc]
DEFAULT_IMAGE_ALIAS = ubuntu-lts
NETWORK_BRIDGE = lxdbr0
STORAGE_POOL = default
DEFAULT_CONTAINER_USER = root
MAIN_INTERFACE = enp0s6
NAT_LISTEN_IP = **********
创建 Systemd 服务文件: 创建一个文件 /etc/systemd/system/lxd-api.service，并填入以下内容 (请根据实际情况修改 User, Group 和 WorkingDirectory, ExecStart 的路径):

[Unit]
Description=LXD Management API Service
After=network.target

[Service]
User=root
Group=root
WorkingDirectory=/root/server
ExecStart=python3 app.py
Restart=always

[Install]
WantedBy=multi-user.target

启动并设置开机自启:
systemctl daemon-reload
systemctl enable lxd-api.service
systemctl start lxd-api.service
systemctl status lxd-api.service
