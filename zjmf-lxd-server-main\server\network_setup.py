#!/usr/bin/env python3
import os
import subprocess
import logging
import sys
from config_handler import app_config

logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s %(levelname)s: %(message)s',
                   datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger('network_setup')

def run_cmd(cmd):
    """执行命令并返回结果"""
    logger.debug(f"执行命令: {cmd}")
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            logger.error(f"命令执行失败 ({process.returncode}): {stderr.decode('utf-8', errors='ignore')}")
            return False
        return True
    except Exception as e:
        logger.error(f"执行命令时出错: {str(e)}")
        return False

def setup_network():
    """配置网络规则"""
    logger.info("开始设置网络规则...")
    
    # 获取配置
    try:
        network_bridge = app_config.network_bridge
        main_interface = app_config.main_interface
        nat_listen_ip = app_config.nat_listen_ip
    except Exception as e:
        logger.error(f"读取配置文件失败: {str(e)}")
        return False
    
    # 确保网络桥接存在
    logger.info(f"检查网络桥接 {network_bridge}...")
    if not run_cmd(f"ip link show {network_bridge} >/dev/null 2>&1"):
        logger.error(f"网络桥接 {network_bridge} 不存在，请确保LXD已正确配置")
        return False
    
    # 启用IP转发
    logger.info("启用IP转发...")
    run_cmd("echo 1 > /proc/sys/net/ipv4/ip_forward")
    run_cmd("echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf")
    run_cmd("sysctl -p")
    
    # 获取桥接网络的子网
    logger.info(f"获取 {network_bridge} 的IP地址和子网...")
    process = subprocess.Popen(f"ip addr show {network_bridge} | grep 'inet ' | awk '{{print $2}}'", 
                               stdout=subprocess.PIPE, shell=True)
    stdout, _ = process.communicate()
    bridge_subnet = stdout.decode('utf-8').strip()
    
    if not bridge_subnet:
        logger.warning(f"无法获取 {network_bridge} 的子网，使用默认 10.0.0.0/8")
        bridge_subnet = "10.0.0.0/8"
    else:
        # 从 CIDR 格式获取子网部分
        ip_parts = bridge_subnet.split('/')
        if len(ip_parts) == 2:
            ip = ip_parts[0].split('.')
            if len(ip) == 4:
                bridge_subnet = f"{ip[0]}.{ip[1]}.{ip[2]}.0/{ip_parts[1]}"
    
    # 添加NAT规则
    logger.info(f"设置NAT规则: {bridge_subnet} -> {main_interface}...")
    run_cmd(f"iptables -t nat -C POSTROUTING -s {bridge_subnet} -o {main_interface} -j MASQUERADE >/dev/null 2>&1 || "
            f"iptables -t nat -A POSTROUTING -s {bridge_subnet} -o {main_interface} -j MASQUERADE")
    
    # 添加FORWARD规则
    logger.info("设置FORWARD规则...")
    run_cmd(f"iptables -C FORWARD -i {network_bridge} -o {main_interface} -j ACCEPT >/dev/null 2>&1 || "
            f"iptables -A FORWARD -i {network_bridge} -o {main_interface} -j ACCEPT")
    run_cmd(f"iptables -C FORWARD -i {main_interface} -o {network_bridge} -m state --state RELATED,ESTABLISHED -j ACCEPT >/dev/null 2>&1 || "
            f"iptables -A FORWARD -i {main_interface} -o {network_bridge} -m state --state RELATED,ESTABLISHED -j ACCEPT")
    
    # 设置LXD网络DNS
    logger.info("配置LXD网络DNS...")
    run_cmd(f"lxc network set {network_bridge} dns.mode=managed >/dev/null 2>&1")
    run_cmd(f"lxc network set {network_bridge} dns.domain=lxd >/dev/null 2>&1")
    
    # 保存iptables规则
    logger.info("持久化iptables规则...")
    # 检查是否安装了iptables-persistent
    if run_cmd("which netfilter-persistent >/dev/null 2>&1"):
        run_cmd("netfilter-persistent save")
    else:
        logger.warning("未安装netfilter-persistent，无法持久化保存iptables规则")
        logger.warning("请安装iptables-persistent: apt install -y iptables-persistent")
    
    # 创建启动脚本
    create_startup_script(network_bridge, main_interface, bridge_subnet)
    
    logger.info("网络规则设置完成")
    return True

def create_startup_script(network_bridge, main_interface, bridge_subnet):
    """创建启动脚本和systemd服务"""
    logger.info("创建系统启动脚本...")
    
    # 创建rc.local脚本
    rc_local_content = f"""#!/bin/bash
# LXD容器网络自启动配置脚本 - 由zjmf-lxd-server生成

# 开启IP转发
echo 1 > /proc/sys/net/ipv4/ip_forward

# 添加NAT规则
iptables -t nat -C POSTROUTING -s {bridge_subnet} -o {main_interface} -j MASQUERADE >/dev/null 2>&1 || \\
iptables -t nat -A POSTROUTING -s {bridge_subnet} -o {main_interface} -j MASQUERADE

# 添加FORWARD规则
iptables -C FORWARD -i {network_bridge} -o {main_interface} -j ACCEPT >/dev/null 2>&1 || \\
iptables -A FORWARD -i {network_bridge} -o {main_interface} -j ACCEPT

iptables -C FORWARD -i {main_interface} -o {network_bridge} -m state --state RELATED,ESTABLISHED -j ACCEPT >/dev/null 2>&1 || \\
iptables -A FORWARD -i {main_interface} -o {network_bridge} -m state --state RELATED,ESTABLISHED -j ACCEPT

exit 0
"""
    with open('/etc/rc.local', 'w') as f:
        f.write(rc_local_content)
    
    os.chmod('/etc/rc.local', 0o755)
    logger.info("已创建 /etc/rc.local")
    
    # 创建systemd服务
    systemd_service_content = """[Unit]
Description=LXD容器网络配置服务
After=network.target lxd.service

[Service]
Type=oneshot
ExecStart=/etc/rc.local
TimeoutSec=0
StandardOutput=journal

[Install]
WantedBy=multi-user.target
"""
    
    try:
        with open('/etc/systemd/system/lxd-network-setup.service', 'w') as f:
            f.write(systemd_service_content)
        
        # 启用服务
        run_cmd("systemctl daemon-reload")
        run_cmd("systemctl enable lxd-network-setup.service")
        logger.info("已创建并启用systemd服务: lxd-network-setup.service")
    except Exception as e:
        logger.error(f"创建systemd服务失败: {str(e)}")

if __name__ == "__main__":
    logger.info("LXD容器网络配置脚本启动")
    if os.geteuid() != 0:
        logger.error("此脚本需要以root权限运行")
        sys.exit(1)
    
    success = setup_network()
    if success:
        logger.info("网络配置成功完成")
        sys.exit(0)
    else:
        logger.error("网络配置失败")
        sys.exit(1) 