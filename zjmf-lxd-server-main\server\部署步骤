1、复制server目录下所有文件到服务器/root目录下
2、安装依赖pip3 install -r requirements.txt
3、配置 app.ini
在 server 目录下，修改一个名为 app.ini 的配置文件。范例文件内容如下：

[server]
HTTP_PORT = 8080
TOKEN = 7215EE9C7D9DC229D2921A40E899EC5F
LOG_LEVEL = INFO

[lxc]
DEFAULT_IMAGE_ALIAS = ubuntu-lts
NETWORK_BRIDGE = lxdbr0
STORAGE_POOL = default
DEFAULT_CONTAINER_USER = root
MAIN_INTERFACE = enp0s6
NAT_LISTEN_IP = **********

4、运行生产环境部署脚本
sudo bash deploy_production_flow.sh

5、创建 Systemd 服务文件: 创建一个文件 /etc/systemd/system/lxd-api.service，并填入以下内容 (请根据实际情况修改 User, Group 和 WorkingDirectory, ExecStart 的路径):

[Unit]
Description=LXD Management API Service
After=network.target

[Service]
User=root
Group=root
WorkingDirectory=/root/server
ExecStart=python3 app.py
Restart=always

[Install]
WantedBy=multi-user.target

6、启动并设置开机自启:
systemctl daemon-reload
systemctl enable lxd-api.service
systemctl start lxd-api.service
systemctl status lxd-api.service