#!/bin/bash

# 流量管理功能安装脚本

set -e

echo "=========================================="
echo "  ZJMF-LXD-Server 流量管理功能安装脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    echo "sudo bash setup_flow_management.sh"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "当前工作目录: $SCRIPT_DIR"

# 检查必要文件是否存在
echo "检查必要文件..."
required_files=(
    "flow_manager.py"
    "flow_reset_scheduler.py"
    "create_flow_management_table.sql"
    "upgrade_flow_database.py"
    "flow_continuity_daemon.py"
    "production_flow_config.py"
    "test_flow_continuity.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
    echo "✅ 找到文件: $file"
done

# 安装Python依赖
echo "安装Python依赖..."
pip3 install python-dateutil sqlite3 2>/dev/null || {
    echo "⚠️  部分依赖可能已安装，继续..."
}

# 初始化/升级数据库
echo "初始化/升级流量管理数据库..."
python3 upgrade_flow_database.py
python3 -c "
from flow_manager import FlowManager
fm = FlowManager()
print('✅ 流量管理数据库初始化完成')
"

# 注册现有容器
echo "注册现有容器到流量管理系统..."
python3 flow_reset_scheduler.py register

# 设置流量连续性守护进程
echo "设置流量连续性守护进程..."
python3 flow_continuity_daemon.py --create-service

# 启用并启动守护进程
systemctl daemon-reload
systemctl enable lxd-flow-continuity.service
systemctl start lxd-flow-continuity.service

if systemctl is-active --quiet lxd-flow-continuity.service; then
    echo "✅ 流量连续性守护进程已启动"
else
    echo "⚠️  流量连续性守护进程启动失败，请检查日志"
fi

# 设置定时任务
echo "设置流量重置定时任务..."

# 检查是否已存在定时任务
if crontab -l 2>/dev/null | grep -q "flow_reset_scheduler.py"; then
    echo "⚠️  流量重置定时任务已存在，跳过添加"
else
    # 添加定时任务：每天凌晨2点检查并重置到期流量，每周日凌晨3点清理已删除容器记录
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/bin/python3 $SCRIPT_DIR/flow_reset_scheduler.py reset >/dev/null 2>&1") | crontab -
    (crontab -l 2>/dev/null; echo "0 3 * * 0 /usr/bin/python3 $SCRIPT_DIR/flow_reset_scheduler.py cleanup >/dev/null 2>&1") | crontab -
    echo "✅ 已添加流量重置定时任务（每天凌晨2点执行）"
    echo "✅ 已添加清理任务（每周日凌晨3点执行）"
fi

# 创建流量管理脚本的快捷命令
echo "创建管理命令..."
cat > /usr/local/bin/lxd-flow-manager << EOF
#!/bin/bash
# LXD流量管理工具

SCRIPT_DIR="$SCRIPT_DIR"

case "\$1" in
    "reset")
        if [ -z "\$2" ]; then
            echo "用法: lxd-flow-manager reset <容器名>"
            exit 1
        fi
        echo "重置容器 \$2 的流量..."
        curl -X POST -H "apikey: \$(grep TOKEN \$SCRIPT_DIR/app.ini | cut -d'=' -f2 | tr -d ' ')" \\
             -H "Content-Type: application/json" \\
             -d "{\"hostname\": \"\$2\"}" \\
             http://localhost:\$(grep HTTP_PORT \$SCRIPT_DIR/app.ini | cut -d'=' -f2 | tr -d ' ')/api/flow/reset
        ;;
    "info")
        if [ -z "\$2" ]; then
            echo "用法: lxd-flow-manager info <容器名>"
            exit 1
        fi
        echo "获取容器 \$2 的流量信息..."
        curl -H "apikey: \$(grep TOKEN \$SCRIPT_DIR/app.ini | cut -d'=' -f2 | tr -d ' ')" \\
             "http://localhost:\$(grep HTTP_PORT \$SCRIPT_DIR/app.ini | cut -d'=' -f2 | tr -d ' ')/api/flow/info?hostname=\$2"
        ;;
    "auto-reset")
        echo "执行自动流量重置..."
        python3 \$SCRIPT_DIR/flow_reset_scheduler.py reset
        ;;
    "register")
        echo "注册现有容器到流量管理系统..."
        python3 \$SCRIPT_DIR/flow_reset_scheduler.py register
        ;;
    "cleanup")
        echo "清理已删除容器的流量管理记录..."
        python3 \$SCRIPT_DIR/flow_reset_scheduler.py cleanup
        ;;
    "test")
        echo "运行流量连续性测试..."
        python3 \$SCRIPT_DIR/test_flow_continuity.py
        ;;
    "upgrade-db")
        echo "升级流量管理数据库..."
        python3 \$SCRIPT_DIR/upgrade_flow_database.py
        ;;
    "daemon-status")
        echo "检查流量连续性守护进程状态..."
        systemctl status lxd-flow-continuity.service
        ;;
    "daemon-restart")
        echo "重启流量连续性守护进程..."
        systemctl restart lxd-flow-continuity.service
        ;;
    "check-now")
        echo "立即检查所有容器流量状态..."
        python3 \$SCRIPT_DIR/flow_continuity_daemon.py
        ;;
    *)
        echo "LXD流量管理工具"
        echo ""
        echo "用法:"
        echo "  lxd-flow-manager reset <容器名>     # 手动重置指定容器流量"
        echo "  lxd-flow-manager info <容器名>      # 查看容器流量管理信息"
        echo "  lxd-flow-manager auto-reset         # 执行自动流量重置"
        echo "  lxd-flow-manager register           # 注册现有容器"
        echo "  lxd-flow-manager cleanup            # 清理已删除容器记录"
        echo "  lxd-flow-manager test               # 运行功能测试"
        echo "  lxd-flow-manager upgrade-db         # 升级数据库结构"
        echo "  lxd-flow-manager daemon-status      # 查看守护进程状态"
        echo "  lxd-flow-manager daemon-restart     # 重启守护进程"
        echo "  lxd-flow-manager check-now          # 立即检查流量状态"
        echo ""
        ;;
esac
EOF

chmod +x /usr/local/bin/lxd-flow-manager

echo ""
echo "=========================================="
echo "  流量管理功能安装完成！"
echo "=========================================="
echo ""
echo "功能说明："
echo "✅ 自动流量重置：每月自动重置容器流量统计"
echo "✅ 手动流量重置：支持通过API手动重置"
echo "✅ 流量历史记录：记录每次重置的历史"
echo "✅ 周期性统计：基于产品开通日期计算月度流量"
echo ""
echo "管理命令："
echo "  lxd-flow-manager reset <容器名>     # 手动重置指定容器流量"
echo "  lxd-flow-manager info <容器名>      # 查看容器流量管理信息"
echo "  lxd-flow-manager auto-reset         # 执行自动流量重置"
echo "  lxd-flow-manager register           # 注册现有容器"
echo ""
echo "定时任务："
echo "  每天凌晨2点自动检查并重置到期的容器流量"
echo ""
echo "API接口："
echo "  POST /api/flow/reset    # 重置容器流量"
echo "  GET  /api/flow/info     # 获取流量管理信息"
echo ""
echo "注意事项："
echo "⚠️  新创建的容器会自动注册到流量管理系统"
echo "⚠️  删除容器时会自动从流量管理系统注销"
echo "⚠️  流量统计基于容器创建日期，每月同一天重置"
echo ""
echo "安装完成！请重启LXD服务以使更改生效："
echo "sudo systemctl restart lxd"
echo "sudo systemctl restart your-lxd-app-service"
