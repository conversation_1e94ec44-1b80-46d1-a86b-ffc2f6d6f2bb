# 流量重置功能文件清单

## 📁 核心功能文件

### ✅ 必需文件（新增）
1. **flow_manager.py** - 流量管理核心类
   - 流量统计计算
   - 重启检测和处理
   - 数据库操作
   - 流量重置逻辑

2. **flow_reset_scheduler.py** - 定时任务脚本
   - 自动流量重置
   - 容器注册/注销
   - 清理已删除容器

3. **flow_continuity_daemon.py** - 生产级守护进程
   - 定期检查容器状态
   - 重启检测
   - 健康监控
   - 故障恢复

4. **create_flow_management_table.sql** - 数据库结构
   - 容器流量管理表
   - 流量重置历史表

5. **upgrade_flow_database.py** - 数据库升级脚本
   - 添加累计流量字段
   - 兼容性处理

6. **production_flow_config.py** - 生产环境配置
   - 不同规模配置
   - 性能参数

7. **test_flow_continuity.py** - 功能测试脚本
   - 流量连续性测试
   - 数据库升级测试

### 📦 安装部署文件
8. **setup_flow_management.sh** - 标准安装脚本
   - 依赖检查
   - 数据库初始化
   - 服务配置

9. **deploy_production_flow.sh** - 生产环境部署脚本
   - 环境检测
   - 自动配置优化
   - 监控设置

### 📚 文档文件
10. **UPGRADE_GUIDE.md** - 现有环境升级指南
11. **FRESH_INSTALL_GUIDE.md** - 全新环境部署指南
12. **FLOW_MANAGEMENT_README.md** - 功能使用说明
13. **FILE_CHECKLIST.md** - 本文件清单

## 🔧 修改的现有文件

### ✅ lxc_manager.py
**修改内容**：
- 添加流量管理器集成
- 延迟初始化避免循环依赖
- 容器创建时自动注册
- 容器删除时自动注销
- 流量统计使用周期计算

**关键修改点**：
```python
# 延迟初始化流量管理器
def _get_flow_manager(self):
    if self.flow_manager is None:
        from flow_manager import FlowManager
        self.flow_manager = FlowManager()
    return self.flow_manager

# 使用周期流量计算
flow_manager = self._get_flow_manager()
if flow_manager:
    used_flow_gb = flow_manager.calculate_current_period_usage(
        hostname, bytes_received_total, bytes_sent_total
    )
```

### ✅ app.py
**修改内容**：
- 添加流量管理API接口
- 延迟初始化流量管理器
- 新增 `/api/flow/reset` 接口
- 新增 `/api/flow/info` 接口

**新增API**：
```python
@app.route('/api/flow/reset', methods=['POST'])
@app.route('/api/flow/info', methods=['GET'])
```

## 🗂️ 运行时生成文件

### 数据库文件
- **flow_management.db** - SQLite数据库
  - container_flow_management 表
  - flow_reset_history 表

### 日志文件
- **flow_reset.log** - 定时任务日志
- **flow_continuity_daemon.log** - 守护进程日志
- **lxd_event_monitor.log** - 事件监听日志（已删除）

### 配置文件
- **production_flow.env** - 生产环境配置
- **/etc/systemd/system/lxd-flow-continuity.service** - 系统服务

### 管理脚本
- **/usr/local/bin/lxd-flow-manager** - 管理命令
- **/usr/local/bin/lxd-flow-monitor** - 监控脚本

## 📋 文件依赖关系

```
app.py
├── flow_manager.py (延迟导入)
└── lxc_manager.py
    └── flow_manager.py (延迟导入)

flow_reset_scheduler.py
├── flow_manager.py
├── lxc_manager.py
└── config_handler.py

flow_continuity_daemon.py
├── flow_manager.py
├── lxc_manager.py
└── config_handler.py

flow_manager.py
├── create_flow_management_table.sql (数据库结构)
└── upgrade_flow_database.py (升级时使用)
```

## ✅ 安装验证清单

### 文件存在性检查
```bash
# 检查核心文件
ls -la flow_manager.py
ls -la flow_reset_scheduler.py
ls -la flow_continuity_daemon.py
ls -la create_flow_management_table.sql
ls -la upgrade_flow_database.py

# 检查安装脚本
ls -la setup_flow_management.sh
ls -la deploy_production_flow.sh

# 检查文档
ls -la *GUIDE.md
ls -la FLOW_MANAGEMENT_README.md
```

### 权限检查
```bash
# 检查执行权限
ls -la *.sh *.py | grep -E "^-rwxr"

# 检查数据库权限
ls -la flow_management.db
```

### 服务检查
```bash
# 检查系统服务
systemctl status lxd-flow-continuity.service

# 检查定时任务
crontab -l | grep flow_reset

# 检查管理命令
which lxd-flow-manager
which lxd-flow-monitor
```

### 功能检查
```bash
# 测试Python导入
python3 -c "
from flow_manager import FlowManager
from flow_reset_scheduler import *
from flow_continuity_daemon import *
print('✅ 所有模块导入成功')
"

# 测试数据库连接
python3 -c "
from flow_manager import FlowManager
fm = FlowManager()
print('✅ 数据库连接成功')
"

# 测试API接口
curl -H "apikey: YOUR_TOKEN" \
     "http://localhost:8060/api/flow/info?hostname=test" \
     2>/dev/null | grep -q "code" && echo "✅ API接口正常"
```

## 🔄 文件更新流程

### 更新单个文件
```bash
# 备份现有文件
cp flow_manager.py flow_manager.py.backup

# 更新文件
# ... 替换文件内容 ...

# 重启相关服务
systemctl restart lxd-flow-continuity.service
systemctl restart your-lxd-service
```

### 批量更新
```bash
# 停止服务
systemctl stop lxd-flow-continuity.service
systemctl stop your-lxd-service

# 备份所有文件
tar -czf backup_$(date +%Y%m%d_%H%M%S).tar.gz *.py *.db *.log

# 更新文件
# ... 替换所有文件 ...

# 运行升级脚本
bash setup_flow_management.sh

# 重启服务
systemctl start your-lxd-service
systemctl start lxd-flow-continuity.service
```

## 📊 文件大小参考

| 文件 | 大小 | 说明 |
|------|------|------|
| flow_manager.py | ~8KB | 核心逻辑 |
| flow_reset_scheduler.py | ~6KB | 定时任务 |
| flow_continuity_daemon.py | ~8KB | 守护进程 |
| create_flow_management_table.sql | ~1KB | 数据库结构 |
| upgrade_flow_database.py | ~2KB | 升级脚本 |
| setup_flow_management.sh | ~5KB | 安装脚本 |
| deploy_production_flow.sh | ~7KB | 生产部署 |
| test_flow_continuity.py | ~6KB | 测试脚本 |
| production_flow_config.py | ~3KB | 配置文件 |
| UPGRADE_GUIDE.md | ~8KB | 升级指南 |
| FRESH_INSTALL_GUIDE.md | ~10KB | 安装指南 |
| FLOW_MANAGEMENT_README.md | ~6KB | 使用说明 |

**总计**: 约 70KB 的新增代码和文档

这个文件清单确保了流量重置功能的完整性和可维护性！
