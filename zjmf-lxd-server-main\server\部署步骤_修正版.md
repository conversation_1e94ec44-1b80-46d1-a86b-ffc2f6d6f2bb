# ZJMF-LXD-Server 新环境部署步骤

## 前提条件
- 已成功安装并初始化 LXD
- 系统为 Ubuntu 20.04+ 或 Debian 11+
- 具有 root 权限

## 部署步骤

### 1. 环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y python3 python3-pip sqlite3 curl wget git cron iptables

# 验证LXD状态
lxc version
lxc list
```

### 2. 复制项目文件
```bash
# 创建项目目录
sudo mkdir -p /root/server
cd /root/server

# 复制server目录下所有文件到 /root/server 目录
# 确保包含以下关键文件：
# - app.py
# - lxc_manager.py
# - config_handler.py
# - flow_manager.py
# - flow_reset_scheduler.py
# - flow_continuity_daemon.py
# - deploy_production_flow.sh
# - requirements.txt
# - 所有其他 .py, .sh, .sql, .md 文件
```

### 3. 安装Python依赖
```bash
# 安装必需的Python包
pip3 install flask pylxd python-dateutil configparser

# 或者使用requirements.txt（推荐）
pip3 install -r requirements.txt

# 验证安装
python3 -c "import flask, pylxd, dateutil; print('✅ 依赖安装成功')"
```

### 4. 配置 app.ini
在 `/root/server` 目录下创建或修改 `app.ini` 文件：

```ini
[server]
HTTP_PORT = 8080
TOKEN = 7215EE9C7D9DC229D2921A40E899EC5F
LOG_LEVEL = INFO

[lxc]
DEFAULT_IMAGE_ALIAS = ubuntu-lts
NETWORK_BRIDGE = lxdbr0
STORAGE_POOL = default
DEFAULT_CONTAINER_USER = root
MAIN_INTERFACE = enp0s6
NAT_LISTEN_IP = **********
```

**重要**：请根据实际环境修改：
- `MAIN_INTERFACE`: 主网卡名称（使用 `ip addr` 查看）
- `NAT_LISTEN_IP`: 服务器的实际IP地址
- `TOKEN`: 建议生成新的安全令牌

### 5. 运行生产环境部署脚本
```bash
cd /root/server
sudo bash deploy_production_flow.sh

# 按提示选择配置，建议：
# - 小规模环境（<10容器）：180秒检查间隔
# - 中等规模环境（10-50容器）：300秒检查间隔
# - 大规模环境（>50容器）：600秒检查间隔
```

### 6. 创建主应用 Systemd 服务
```bash
sudo cat > /etc/systemd/system/lxd-api.service << 'EOF'
[Unit]
Description=LXD Management API Service
After=network.target lxd.service
Wants=lxd.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/server
ExecStart=/usr/bin/python3 /root/server/app.py
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# 环境变量
Environment=PYTHONPATH=/root/server

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=lxd-api

[Install]
WantedBy=multi-user.target
EOF
```

### 7. 启动并设置开机自启
```bash
sudo systemctl daemon-reload
sudo systemctl enable lxd-api.service
sudo systemctl start lxd-api.service

# 检查服务状态
sudo systemctl status lxd-api.service
sudo systemctl status lxd-flow-continuity.service
```

### 8. 验证部署
```bash
# 检查Web服务
curl -H "apikey: 7215EE9C7D9DC229D2921A40E899EC5F" \
     "http://localhost:8080/api/containers"

# 检查流量管理功能
lxd-flow-manager

# 运行功能测试
python3 test_flow_continuity.py

# 检查日志
sudo journalctl -u lxd-api.service -f
sudo journalctl -u lxd-flow-continuity.service -f
```

### 9. 防火墙配置（可选）
```bash
# 开放Web服务端口
sudo ufw allow 8080/tcp

# 或者使用iptables
sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
sudo iptables-save > /etc/iptables/rules.v4
```

## 部署完成检查清单

- [ ] LXD服务正常运行
- [ ] Python依赖安装成功
- [ ] app.ini配置正确
- [ ] 流量管理功能部署成功
- [ ] lxd-api.service 运行正常
- [ ] lxd-flow-continuity.service 运行正常
- [ ] Web界面可以访问（http://服务器IP:8080）
- [ ] API接口响应正常
- [ ] 定时任务设置成功
- [ ] 管理命令可用

## 常见问题

### 1. 服务启动失败
```bash
# 查看详细错误
sudo journalctl -u lxd-api.service -n 50
sudo journalctl -u lxd-flow-continuity.service -n 50

# 手动测试
cd /root/server
python3 app.py
```

### 2. 权限问题
```bash
# 设置正确权限
sudo chown -R root:root /root/server
sudo chmod +x /root/server/*.sh /root/server/*.py
```

### 3. 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep :8080

# 修改app.ini中的HTTP_PORT
```

### 4. 网络配置问题
```bash
# 检查网卡名称
ip addr show

# 检查IP地址
ip route show default
```

部署完成后，您将拥有一个功能完整的LXD容器管理系统，包括：
- Web管理界面
- RESTful API
- 自动流量重置
- 重启后流量连续性
- 完整的监控和日志
