#!/bin/bash
#
# LXD 完全清理脚本
# 作者: ji<PERSON><PERSON><PERSON>
# 用途: 完全删除LXD安装，为重新安装做准备
#

set -e

# 颜色定义
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_RED='\033[0;31m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_NC='\033[0m'

msg_info() { echo -e "${COLOR_CYAN}[*] $1${COLOR_NC}"; }
msg_ok() { echo -e "${COLOR_GREEN}[+] $1${COLOR_NC}"; }
msg_error() { echo -e "${COLOR_RED}[!] $1${COLOR_NC}"; }
msg_warn() { echo -e "${COLOR_YELLOW}[-] $1${COLOR_NC}"; }

# 检查root权限
check_root() {
    if [[ "${EUID}" -ne 0 ]]; then
        msg_error "此脚本需要 root 权限，请使用 'sudo' 运行。"
        exit 1
    fi
}

# 主清理函数
cleanup_lxd() {
    msg_info "开始清理 LXD 安装..."
    echo ""
    
    # 1. 停止所有容器
    msg_info "步骤 1/12: 停止所有LXD容器..."
    if command -v lxc &>/dev/null; then
        lxc stop --all 2>/dev/null || true
        msg_ok "容器已停止"
    else
        msg_warn "lxc 命令不存在，跳过"
    fi
    
    # 2. 删除所有容器
    msg_info "步骤 2/12: 删除所有容器..."
    if command -v lxc &>/dev/null; then
        for container in $(lxc list --format csv -c n 2>/dev/null | cut -d, -f1); do
            if [[ -n "$container" ]]; then
                lxc delete "$container" --force 2>/dev/null || true
                msg_ok "删除容器: $container"
            fi
        done
    fi
    
    # 3. 删除所有镜像
    msg_info "步骤 3/12: 删除所有镜像..."
    if command -v lxc &>/dev/null; then
        for image in $(lxc image list --format csv -c f 2>/dev/null | cut -d, -f1); do
            if [[ -n "$image" ]]; then
                lxc image delete "$image" 2>/dev/null || true
                msg_ok "删除镜像: $image"
            fi
        done
    fi
    
    # 4. 删除存储池
    msg_info "步骤 4/12: 删除存储池..."
    if command -v lxc &>/dev/null; then
        for pool in $(lxc storage list --format csv 2>/dev/null | cut -d, -f1 | grep -v "^default$"); do
            if [[ -n "$pool" ]]; then
                lxc storage delete "$pool" 2>/dev/null || true
                msg_ok "删除存储池: $pool"
            fi
        done
    fi
    
    # 5. 删除网络
    msg_info "步骤 5/12: 删除网络配置..."
    if command -v lxc &>/dev/null; then
        for network in $(lxc network list --format csv 2>/dev/null | cut -d, -f1 | grep -v "^default$"); do
            if [[ -n "$network" ]]; then
                lxc network delete "$network" 2>/dev/null || true
                msg_ok "删除网络: $network"
            fi
        done
    fi
    
    # 6. 删除LXD snap
    msg_info "步骤 6/12: 删除 LXD snap 包..."
    if snap list lxd &>/dev/null; then
        snap remove lxd 2>/dev/null || true
        msg_ok "LXD snap 包已删除"
    else
        msg_warn "LXD snap 包不存在"
    fi
    
    # 7. 删除core snap（可选）
    msg_info "步骤 7/12: 删除 core snap 包..."
    read -p "是否删除 snap core? 这会影响其他snap应用 (y/N): " remove_core
    if [[ "${remove_core}" =~ ^[yY]$ ]]; then
        snap remove core 2>/dev/null || true
        msg_ok "Core snap 包已删除"
    else
        msg_warn "保留 core snap 包"
    fi
    
    # 8. 删除snapd（可选）
    msg_info "步骤 8/12: 删除 snapd..."
    read -p "是否完全删除 snapd? 这会删除所有snap功能 (y/N): " remove_snapd
    if [[ "${remove_snapd}" =~ ^[yY]$ ]]; then
        apt remove --purge snapd -y 2>/dev/null || true
        apt autoremove -y 2>/dev/null || true
        msg_ok "Snapd 已删除"
    else
        msg_warn "保留 snapd"
    fi
    
    # 9. 清理数据目录
    msg_info "步骤 9/12: 清理数据目录..."
    rm -rf /var/snap/lxd 2>/dev/null || true
    rm -rf /var/lib/lxd 2>/dev/null || true
    rm -rf /etc/lxd 2>/dev/null || true
    rm -rf /root/.config/lxc 2>/dev/null || true
    rm -rf /root/.local/share/lxc 2>/dev/null || true
    msg_ok "数据目录已清理"
    
    # 10. 删除符号链接
    msg_info "步骤 10/12: 删除符号链接..."
    rm -f /usr/local/bin/lxd 2>/dev/null || true
    rm -f /usr/local/bin/lxc 2>/dev/null || true
    msg_ok "符号链接已删除"
    
    # 11. 清理PATH配置
    msg_info "步骤 11/12: 清理PATH配置..."
    rm -f /etc/profile.d/snap_path.sh 2>/dev/null || true
    msg_ok "PATH配置已清理"
    
    # 12. 清理网络配置
    msg_info "步骤 12/12: 清理网络配置..."
    # 删除可能的lxdbr0网桥
    if ip link show lxdbr0 &>/dev/null; then
        ip link delete lxdbr0 2>/dev/null || true
        msg_ok "删除 lxdbr0 网桥"
    fi
    
    echo ""
    msg_ok "==============================================="
    msg_ok "LXD 清理完成！"
    msg_ok "==============================================="
    echo ""
    msg_warn "建议重启系统以确保所有服务完全停止："
    msg_warn "sudo reboot"
    echo ""
    msg_info "重启后可以重新运行 LinuxTools 脚本安装 LXD"
}

# 主函数
main() {
    check_root
    
    echo ""
    msg_warn "==============================================="
    msg_warn "警告: 此操作将完全删除 LXD 及其所有数据！"
    msg_warn "包括: 容器、镜像、网络、存储池等"
    msg_warn "==============================================="
    echo ""
    
    read -p "确认要继续吗? (y/N): " confirm
    if [[ ! "${confirm}" =~ ^[yY]$ ]]; then
        msg_info "操作已取消"
        exit 0
    fi
    
    cleanup_lxd
}

main "$@"
