# linuxtools
自用linux小工具脚本

## 使用方法

### 方法1：首次运行（自动安装快捷命令）
~~~bash
bash <(curl -s https://raw.githubusercontent.com/jiayihello/LinuxTools/main/linuxtools.sh)
~~~
或者
~~~bash
wget -qO- https://raw.githubusercontent.com/jiayihello/LinuxTools/main/linuxtools.sh | bash
~~~

**首次运行后会自动安装快捷命令 `xue`**

### 方法2：使用快捷命令（推荐）
安装快捷命令后，可以直接使用：
~~~bash
xue
~~~
或者
~~~bash
sudo xue
~~~

### 方法3：手动安装快捷命令
如果自动安装失败，可以在菜单中选择 `9) 安装快捷命令` 手动安装。

> **注意**：快捷命令需要root权限安装，将被安装到 `/usr/local/bin/xue`
### 项目截图

![image](https://github.com/user-attachments/assets/09214f76-8575-43b4-9c86-1c0a3cf63f60)
