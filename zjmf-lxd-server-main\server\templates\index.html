<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LXD Web 管理器</title>
    <link rel="icon" href="https://linuxcontainers.org/static/img/containers.small.png" type="image/png">
    <link rel="shortcut icon" href="https://linuxcontainers.org/static/img/containers.small.png" type="image/png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/xterm@5.3.0/css/xterm.css" />
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="{% if not session.logged_in %}d-flex justify-content-center align-items-center min-vh-100 bg-light{% else %}bg-light{% endif %}">
    {% if session.logged_in %}
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>LXD Web 管理器</h1>
            <a href="{{ url_for('logout') }}" class="btn btn-outline-danger btn-sm">退出登录</a>
        </div>

        {% if incus_error[0] %}
        <div class="alert alert-danger" role="alert">
            <strong>错误:</strong> 无法连接到 Incus 或执行命令失败。显示的数据可能来自数据库缓存。 <br>
            详细信息: {{ incus_error[1] }}
        </div>
        {% endif %}

        <div class="row gx-4 main-content-wrapper">
            <div class="col-12">
                 <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2 class="mb-0">容器列表</h2>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm d-flex align-items-center" onclick="location.reload()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-clockwise me-1" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2z"/>
                                <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466"/>
                            </svg>
                            刷新列表
                        </button>
                    </div>
                </div>

                <input type="search" id="searchInput" class="form-control mb-3" placeholder="按名称搜索...">

                <div class="container-list-desktop d-none d-md-block">
                    <div class="custom-container-list-header">
                        <div class="custom-col" style="flex: 1.5; min-width: 110px;">名称</div>
                        <div class="custom-col" style="flex: 0.8; min-width: 70px; text-align: center;">状态</div>
                        <div class="custom-col sortable" data-sort="cpu" style="flex: 0.8; min-width: 70px;">CPU占用<span class="sort-icon"></span></div>
                        <div class="custom-col sortable" data-sort="mem" style="flex: 1.2; min-width: 110px;">内存占用<span class="sort-icon"></span></div>
                        <div class="custom-col sortable" data-sort="disk" style="flex: 1.2; min-width: 110px;">硬盘占用<span class="sort-icon"></span></div>
                        <div class="custom-col" style="flex: 1.2; min-width: 100px;">IP 地址</div>
                        <div class="custom-col sortable" data-sort="rx" style="flex: 1; min-width: 80px;">网络 RX<span class="sort-icon"></span></div>
                        <div class="custom-col sortable" data-sort="tx" style="flex: 1; min-width: 80px;">网络 TX<span class="sort-icon"></span></div>
                        <div class="custom-col sortable" data-sort="flow" style="flex: 1.2; min-width: 110px;">总流量<span class="sort-icon"></span></div>
                        <div class="custom-col" style="flex: 1.5; min-width: 120px;">镜像</div>
                        <div class="custom-col" style="flex: 1.2; min-width: 90px;">创建时间</div>
                        <div class="custom-col" style="flex: 1; min-width: 70px; text-align: center;">操作</div>
                    </div>
                    <div id="containerListDesktopItems">
                        {% for c in containers %}
                        <div class="custom-container-list-row" data-container-name="{{ c.name }}" data-container-status="{{ c.status }}">
                            <div class="custom-col col-name" style="flex: 1.5; min-width: 110px;"><div class="truncate-text" title="{{ c.name }}">{{ c.name }}</div></div>
                            <div class="custom-col col-status" style="flex: 0.8; min-width: 70px; text-align: center;"><span class="badge bg-{% if c.status == 'Running' %}success{% elif c.status == 'Stopped' %}danger{% else %}secondary{% endif %}">{{ c.status }}</span></div>
                            
                            <div class="custom-col col-cpu" style="flex: 0.8; min-width: 70px;">
                                {% if c.status == 'Running' %}<strong id="cpu-{{ c.name }}">{{ c.cpu_usage }}</strong> %{% else %}<span class="text-muted">N/A</span>{% endif %}
                            </div>
                            <div class="custom-col col-mem" style="flex: 1.2; min-width: 110px;">
                                {% if c.status == 'Running' %}<strong id="mem-{{ c.name }}">{{ c.mem_usage }}</strong> / {{ c.mem_total }} MB{% else %}<span class="text-muted">N/A</span>{% endif %}
                            </div>
                            <div class="custom-col col-disk" style="flex: 1.2; min-width: 110px;">
                                {% if c.status == 'Running' %}<strong id="disk-{{ c.name }}">{{ c.disk_usage }}</strong> / {{ c.disk_total }} MB{% else %}<span class="text-muted">N/A</span>{% endif %}
                            </div>

                            <div class="custom-col col-ip" style="flex: 1.2; min-width: 100px;"><div class="truncate-text" title="{{ c.ip if c.ip and c.ip != 'N/A' else '-' }}">{{ c.ip if c.ip and c.ip != 'N/A' else '-' }}</div></div>

                            <div class="custom-col col-rx" style="flex: 1; min-width: 80px;">
                                {% if c.status == 'Running' %}<strong id="rx-{{ c.name }}">-</strong> KB/s{% else %}<span class="text-muted">N/A</span>{% endif %}
                            </div>
                            <div class="custom-col col-tx" style="flex: 1; min-width: 80px;">
                                {% if c.status == 'Running' %}<strong id="tx-{{ c.name }}">-</strong> KB/s{% else %}<span class="text-muted">N/A</span>{% endif %}
                            </div>
                            <div class="custom-col col-flow" style="flex: 1.2; min-width: 110px;">
                                {% if c.status == 'Running' %}<strong id="flow-{{ c.name }}">{{ c.total_flow }}</strong> / {{ c.flow_limit if c.flow_limit > 0 else '∞' }} GB{% else %}<span class="text-muted">N/A</span>{% endif %}
                            </div>

                            <div class="custom-col col-image" style="flex: 1.5; min-width: 120px;"><div class="truncate-text" title="{{ c.image_source if c.image_source else 'N/A' }}">{{ c.image_source if c.image_source else 'N/A' }}</div></div>
                            <div class="custom-col col-created" style="flex: 1.2; min-width: 90px;"><div class="truncate-text" title="{{ c.created_at.split('T')[0] if c.created_at else 'N/A' }}">{{ c.created_at.split('T')[0] if c.created_at else 'N/A' }}</div></div>
                            <div class="custom-col col-actions" style="flex: 1; min-width: 70px; text-align: center;">
                                <div class="dropdown actions-dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">操作</button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><button class="dropdown-item" onclick="showInfo('{{ c.name }}', this)">信息</button></li>
                                        <li><button class="dropdown-item" onclick="showNatRuleModal('{{ c.name }}', this)">NAT 规则</button></li>
                                        {% if c.status == 'Running' %}
                                        <li><button class="dropdown-item" onclick="performAction('{{ c.name }}', 'stop', this)">停止</button></li>
                                        <li><button class="dropdown-item" onclick="performAction('{{ c.name }}', 'restart', this)">重启</button></li>
                                        {% elif c.status == 'Stopped' %}
                                        <li><button class="dropdown-item" onclick="performAction('{{ c.name }}', 'start', this)">启动</button></li>
                                        {% endif %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><button class="dropdown-item text-danger" onclick="performAction('{{ c.name }}', 'delete', this)">删除</button></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {% else %}
                            <div class="py-3 text-center">没有找到容器。</div>
                        {% endfor %}
                    </div>
                </div>

                <div class="container-list-mobile d-block d-md-none mt-3">
                    {% for c in containers %}
                    <div class="card mb-3 shadow-sm" data-container-name="{{ c.name }}" data-container-status="{{ c.status }}">
                         <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title mb-0 me-2">{{ c.name }}</h5>
                                <span class="badge bg-{% if c.status == 'Running' %}success{% elif c.status == 'Stopped' %}danger{% else %}secondary{% endif %}">{{ c.status }}</span>
                            </div>
                            <p class="card-text mb-2"><small class="text-muted">IP:</small> {{ c.ip if c.ip and c.ip != 'N/A' else '-' }}</p>

                            {% if c.status == 'Running' %}
                            <div class="stats-grid" style="font-size: 0.9em; line-height: 1.5;">
                                <div class="row gx-3">
                                    <div class="col-6"><small class="text-muted">CPU:</small> <strong id="cpu-mobile-{{ c.name }}">{{ c.cpu_usage }}</strong> %</div>
                                    <div class="col-6"><small class="text-muted">内存:</small> <strong id="mem-mobile-{{ c.name }}">{{ c.mem_usage }}</strong><small>/{{ c.mem_total }} MB</small></div>
                                    <div class="col-6"><small class="text-muted">硬盘:</small> <strong id="disk-mobile-{{ c.name }}">{{ c.disk_usage }}</strong><small>/{{ c.disk_total }} MB</small></div>
                                    <div class="col-6"><small class="text-muted">流量:</small> <strong id="flow-mobile-{{ c.name }}">{{ c.total_flow }}</strong><small>/{{ c.flow_limit if c.flow_limit > 0 else '∞' }} GB</small></div>
                                    <div class="col-6"><small class="text-muted">RX:</small> <strong id="rx-mobile-{{ c.name }}">-</strong> <small>KB/s</small></div>
                                    <div class="col-6"><small class="text-muted">TX:</small> <strong id="tx-mobile-{{ c.name }}">-</strong> <small>KB/s</small></div>
                                </div>
                            </div>
                            {% else %}
                            <p class="text-muted"><small>监控信息 N/A (容器已停止)</small></p>
                            {% endif %}
                            
                            <p class="card-text mb-1 mt-2"><small class="text-muted">镜像:</small> <span class="truncate-text" title="{{ c.image_source if c.image_source else 'N/A' }}" style="max-width: 200px;">{{ c.image_source if c.image_source else 'N/A' }}</span></p>
                            <p class="card-text mb-3"><small class="text-muted">创建:</small> {{ c.created_at.split('T')[0] if c.created_at else 'N/A' }}</p>
                            <div class="card-actions">
                                <div class="dropdown actions-dropdown w-100">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">操作</button>
                                     <ul class="dropdown-menu dropdown-menu-end w-100">
                                        <li><button class="dropdown-item" onclick="showInfo('{{ c.name }}', this)">信息</button></li>
                                        <li><button class="dropdown-item" onclick="showNatRuleModal('{{ c.name }}', this)">NAT 规则</button></li>
                                        {% if c.status == 'Running' %}
                                        <li><button class="dropdown-item" onclick="performAction('{{ c.name }}', 'stop', this)">停止</button></li>
                                        <li><button class="dropdown-item" onclick="performAction('{{ c.name }}', 'restart', this)">重启</button></li>
                                        {% elif c.status == 'Stopped' %}
                                        <li><button class="dropdown-item" onclick="performAction('{{ c.name }}', 'start', this)">启动</button></li>
                                        {% endif %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><button class="dropdown-item text-danger" onclick="performAction('{{ c.name }}', 'delete', this)">删除</button></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info text-center" role="alert">没有找到容器。</div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div id="loginContainer">
        <h2 class="text-center mb-4">管理员登录</h2>
        <form method="POST" action="{{ url_for('login') }}">
            <input type="hidden" name="next" value="{{ request.args.get('next', '') }}">
            <div class="mb-3">
                <label for="password" class="form-label">密码 (Token)</label>
                <input type="password" class="form-control" id="password" name="password" required autofocus>
            </div>
            {% if login_error %}
            <div class="alert alert-danger" role="alert">{{ login_error }}</div>
            {% endif %}
            <button type="submit" class="btn btn-primary w-100">登录</button>
        </form>
    </div>
    {% endif %}

    <div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer"></div>

    <div class="modal fade" id="infoModal" tabindex="-1"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="infoModalLabel">容器信息</h5><button type="button" class="btn-close" data-bs-dismiss="modal"></button></div><div class="modal-body"><div id="basicInfoContent">...</div><div id="infoError" class="alert alert-danger mt-2 d-none"></div></div></div></div></div>
    <div class="modal fade" id="natRuleModal" tabindex="-1"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="natRuleModalLabel">NAT 规则</h5><button type="button" class="btn-close" data-bs-dismiss="modal"></button></div><div class="modal-body"><div id="natRulesList"><ul id="natRulesContent"><li>...</li></ul><div id="natRulesError" class="alert alert-warning mt-2 d-none"></div></div></div></div></div>
    <div class="modal fade" id="confirmModal" tabindex="-1"><div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="confirmModalLabel">请确认</h5><button type="button" class="btn-close" data-bs-dismiss="modal"></button></div><div class="modal-body" id="confirmModalBody"></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button><button type="button" class="btn btn-primary" id="confirmActionButton">确认</button></div></div></div></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>