# 全新环境部署指南

## 📋 概述

本指南适用于在全新环境中部署带有流量重置功能的ZJMF-LXD-Server系统。

## 🔧 系统要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 磁盘: 20GB以上可用空间
- 网络: 稳定的网络连接

### 软件要求
- Ubuntu 20.04+ / Debian 11+ / CentOS 8+
- Python 3.8+
- LXD 4.0+
- SQLite 3.0+

## 🚀 安装步骤

### 步骤1：系统准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y python3 python3-pip sqlite3 curl wget git cron

# 安装LXD（如果未安装）
sudo snap install lxd
sudo lxd init  # 按提示配置
```

### 步骤2：安装Python依赖
```bash
# 安装必需的Python包
sudo pip3 install flask pylxd python-dateutil

# 验证安装
python3 -c "
import flask, pylxd, dateutil
print('✅ Python依赖安装成功')
"
```

### 步骤3：下载项目代码
```bash
# 创建项目目录
sudo mkdir -p /opt/zjmf-lxd-server
cd /opt/zjmf-lxd-server

# 下载代码（假设从Git仓库）
# git clone https://github.com/your-repo/zjmf-lxd-server.git .

# 或者手动创建目录结构
mkdir -p server
cd server

# 确保所有必需文件存在：
# - app.py
# - lxc_manager.py
# - config_handler.py
# - flow_manager.py
# - flow_reset_scheduler.py
# - flow_continuity_daemon.py
# - create_flow_management_table.sql
# - upgrade_flow_database.py
# - production_flow_config.py
# - test_flow_continuity.py
# - setup_flow_management.sh
# - deploy_production_flow.sh
# - app.ini (配置文件)
```

### 步骤4：配置系统
```bash
cd /opt/zjmf-lxd-server/server

# 创建配置文件（如果不存在）
cat > app.ini << 'EOF'
[DEFAULT]
HTTP_PORT = 8060
TOKEN = your-secure-token-here
LOG_LEVEL = INFO

[LXD]
# LXD配置
ENDPOINT = unix:///var/snap/lxd/common/lxd/unix.socket

[FLOW]
# 流量管理配置
DAEMON_ENABLED = true
DAEMON_INTERVAL = 300
LOG_LEVEL = INFO
EOF

# 设置权限
sudo chown -R root:lxd /opt/zjmf-lxd-server
sudo chmod -R 755 /opt/zjmf-lxd-server
sudo chmod +x *.sh *.py
```

### 步骤5：部署流量管理功能
```bash
# 运行生产环境部署脚本
sudo bash deploy_production_flow.sh

# 或者运行标准安装脚本
# sudo bash setup_flow_management.sh
```

### 步骤6：创建系统服务
```bash
# 创建主应用服务
sudo cat > /etc/systemd/system/zjmf-lxd-server.service << 'EOF'
[Unit]
Description=ZJMF LXD Server
After=network.target lxd.service
Wants=lxd.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/zjmf-lxd-server/server
ExecStart=/usr/bin/python3 /opt/zjmf-lxd-server/server/app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable zjmf-lxd-server.service
sudo systemctl start zjmf-lxd-server.service
```

### 步骤7：验证安装
```bash
# 检查主服务状态
sudo systemctl status zjmf-lxd-server.service

# 检查流量管理守护进程
sudo systemctl status lxd-flow-continuity.service

# 测试API接口
curl -H "apikey: your-secure-token-here" \
     "http://localhost:8060/api/containers"

# 运行功能测试
python3 test_flow_continuity.py
```

## 🔧 配置优化

### 1. 根据环境规模调整配置
```bash
# 编辑生产配置
sudo nano production_flow.env

# 小规模环境（<10个容器）
FLOW_DAEMON_INTERVAL=180  # 3分钟

# 中等规模环境（10-50个容器）
FLOW_DAEMON_INTERVAL=300  # 5分钟

# 大规模环境（>50个容器）
FLOW_DAEMON_INTERVAL=600  # 10分钟

# 重启服务应用配置
sudo systemctl restart lxd-flow-continuity.service
```

### 2. 设置防火墙
```bash
# 开放HTTP端口
sudo ufw allow 8060/tcp

# 或者使用iptables
sudo iptables -A INPUT -p tcp --dport 8060 -j ACCEPT
```

### 3. 配置反向代理（可选）
```bash
# 安装Nginx
sudo apt install nginx

# 创建配置文件
sudo cat > /etc/nginx/sites-available/zjmf-lxd << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8060;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/zjmf-lxd /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 📊 监控和维护

### 1. 设置监控
```bash
# 查看系统状态
lxd-flow-monitor

# 查看服务日志
sudo journalctl -u zjmf-lxd-server.service -f
sudo journalctl -u lxd-flow-continuity.service -f

# 查看流量管理状态
lxd-flow-manager daemon-status
```

### 2. 定期维护任务
```bash
# 添加到crontab
sudo crontab -e

# 添加以下内容：
# 每天凌晨备份数据库
0 1 * * * cp /opt/zjmf-lxd-server/server/flow_management.db /opt/zjmf-lxd-server/server/flow_management.db.backup.$(date +\%Y\%m\%d)

# 每周清理旧备份
0 2 * * 0 find /opt/zjmf-lxd-server/server -name "*.backup.*" -mtime +7 -delete

# 每月检查系统健康
0 3 1 * * /usr/local/bin/lxd-flow-monitor >> /var/log/monthly-health-check.log
```

### 3. 日志管理
```bash
# 检查日志大小
du -sh /opt/zjmf-lxd-server/server/*.log

# 手动轮转日志
sudo logrotate -f /etc/logrotate.d/lxd-flow-management
```

## 🧪 测试和验证

### 1. 创建测试容器
```bash
# 通过API创建容器
curl -X POST \
     -H "apikey: your-secure-token-here" \
     -H "Content-Type: application/json" \
     -d '{
       "hostname": "test-container",
       "image": "ubuntu:20.04",
       "cpu": 1,
       "memory": 512,
       "disk": 1024,
       "bandwidth": 100
     }' \
     http://localhost:8060/api/create
```

### 2. 测试流量管理功能
```bash
# 查看容器流量信息
lxd-flow-manager info test-container

# 手动重置流量
lxd-flow-manager reset test-container

# 测试API接口
curl -H "apikey: your-secure-token-here" \
     "http://localhost:8060/api/flow/info?hostname=test-container"
```

### 3. 测试重启连续性
```bash
# 重启容器
lxc restart test-container

# 等待几秒后检查流量连续性
sleep 10
lxd-flow-manager info test-container

# 应该看到流量统计保持连续
```

## 🔒 安全配置

### 1. 更改默认TOKEN
```bash
# 生成安全的TOKEN
TOKEN=$(openssl rand -hex 32)
echo "新TOKEN: $TOKEN"

# 更新配置文件
sudo sed -i "s/TOKEN = .*/TOKEN = $TOKEN/" app.ini

# 重启服务
sudo systemctl restart zjmf-lxd-server.service
```

### 2. 限制访问
```bash
# 只允许特定IP访问
sudo ufw allow from ***********/24 to any port 8060

# 或者绑定到内网IP
# 编辑app.py中的app.run()参数
```

### 3. 启用HTTPS（推荐）
```bash
# 获取SSL证书（Let's Encrypt）
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 或者使用自签名证书
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/zjmf-lxd.key \
    -out /etc/ssl/certs/zjmf-lxd.crt
```

## ✅ 部署完成检查清单

- [ ] 系统依赖安装完成
- [ ] Python依赖安装成功
- [ ] 项目代码部署完成
- [ ] 配置文件设置正确
- [ ] 主服务运行正常
- [ ] 流量管理守护进程运行正常
- [ ] 定时任务设置成功
- [ ] API接口测试通过
- [ ] 流量重置功能正常
- [ ] 重启连续性测试通过
- [ ] 监控脚本运行正常
- [ ] 日志轮转配置完成
- [ ] 安全配置完成
- [ ] 防火墙规则设置
- [ ] 备份策略配置

## 🎉 部署完成

恭喜！您已经成功部署了带有完整流量重置功能的ZJMF-LXD-Server系统。

### 主要功能
- ✅ 容器生命周期管理
- ✅ 网络端口映射
- ✅ 流量统计和重置
- ✅ 每月自动流量重置
- ✅ 重启后流量连续性
- ✅ 手动流量重置
- ✅ 完整的API接口
- ✅ 生产级监控和日志

### 管理命令
```bash
# 查看系统状态
lxd-flow-monitor

# 管理容器流量
lxd-flow-manager info <容器名>
lxd-flow-manager reset <容器名>

# 查看服务状态
systemctl status zjmf-lxd-server.service
systemctl status lxd-flow-continuity.service

# 查看日志
journalctl -u zjmf-lxd-server.service -f
journalctl -u lxd-flow-continuity.service -f
```

系统现在已经准备好为您提供稳定可靠的容器管理服务！
