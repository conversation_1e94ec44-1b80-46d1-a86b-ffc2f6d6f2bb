# 现有环境升级指南

## 📋 概述

本指南适用于已经搭建了旧版本ZJMF-LXD-Server并创建了很多容器的环境，需要添加流量重置功能。

## ⚠️ 升级前准备

### 1. 备份重要数据
```bash
# 备份现有数据库（如果有）
cp *.db *.db.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# 备份配置文件
cp app.ini app.ini.backup.$(date +%Y%m%d_%H%M%S)

# 备份现有脚本
cp lxc_manager.py lxc_manager.py.backup.$(date +%Y%m%d_%H%M%S)
cp app.py app.py.backup.$(date +%Y%m%d_%H%M%S)
```

### 2. 检查环境状态
```bash
# 检查当前容器数量
lxc list --format csv | wc -l

# 检查服务状态
systemctl status your-lxd-service  # 替换为实际服务名

# 检查磁盘空间
df -h
```

### 3. 停止相关服务
```bash
# 停止LXD管理服务（根据实际情况调整）
systemctl stop your-lxd-service

# 或者如果是直接运行的Python脚本
pkill -f "python.*app.py"
```

## 🔧 升级步骤

### 步骤1：安装Python依赖
```bash
# 安装新增的Python包
pip3 install python-dateutil

# 验证安装
python3 -c "from dateutil.relativedelta import relativedelta; print('✅ python-dateutil 安装成功')"
```

### 步骤2：下载新增文件
确保以下文件存在于server目录中：
- `flow_manager.py` - 流量管理核心类
- `flow_reset_scheduler.py` - 定时任务脚本
- `flow_continuity_daemon.py` - 守护进程
- `create_flow_management_table.sql` - 数据库结构
- `upgrade_flow_database.py` - 数据库升级脚本
- `production_flow_config.py` - 生产环境配置
- `test_flow_continuity.py` - 测试脚本
- `setup_flow_management.sh` - 安装脚本
- `deploy_production_flow.sh` - 生产部署脚本

### 步骤3：运行升级脚本
```bash
cd /path/to/your/zjmf-lxd-server/server

# 给脚本执行权限
chmod +x *.sh *.py

# 运行升级安装
sudo bash setup_flow_management.sh
```

### 步骤4：验证升级结果
```bash
# 检查数据库是否创建成功
ls -la flow_management.db

# 检查守护进程状态
systemctl status lxd-flow-continuity.service

# 检查定时任务
crontab -l | grep flow_reset

# 运行测试
python3 test_flow_continuity.py
```

### 步骤5：注册现有容器
```bash
# 将现有容器注册到流量管理系统
lxd-flow-manager register

# 检查注册结果
sqlite3 flow_management.db "SELECT hostname, created_date, flow_limit_gb FROM container_flow_management;"
```

### 步骤6：重启服务
```bash
# 重启LXD管理服务
systemctl restart your-lxd-service

# 检查服务状态
systemctl status your-lxd-service
```

## 🔍 升级验证

### 1. 功能测试
```bash
# 测试API接口
curl -H "apikey: YOUR_TOKEN" \
     "http://localhost:8060/api/flow/info?hostname=test-container"

# 测试手动重置
lxd-flow-manager reset test-container

# 查看容器流量信息
lxd-flow-manager info test-container
```

### 2. 监控检查
```bash
# 查看守护进程日志
journalctl -u lxd-flow-continuity.service -f

# 运行健康检查
lxd-flow-monitor

# 检查流量统计
lxd-flow-manager check-now
```

## 🚨 常见问题处理

### 问题1：导入错误
```
ImportError: No module named 'dateutil'
```
**解决方案**：
```bash
pip3 install python-dateutil
# 或者
apt-get install python3-dateutil
```

### 问题2：权限错误
```
Permission denied: 'flow_management.db'
```
**解决方案**：
```bash
chown lxd:lxd flow_management.db
chmod 664 flow_management.db
```

### 问题3：服务启动失败
```
systemctl status lxd-flow-continuity.service
```
**解决方案**：
```bash
# 查看详细错误
journalctl -u lxd-flow-continuity.service -n 50

# 手动测试
python3 flow_continuity_daemon.py

# 重新创建服务
python3 flow_continuity_daemon.py --create-service
systemctl daemon-reload
systemctl restart lxd-flow-continuity.service
```

### 问题4：现有容器流量异常
如果升级后发现某些容器的流量统计异常：
```bash
# 手动重置问题容器的流量基准
lxd-flow-manager reset container-name

# 或者重新注册容器
sqlite3 flow_management.db "DELETE FROM container_flow_management WHERE hostname='container-name';"
lxd-flow-manager register
```

## 📊 升级后配置优化

### 1. 根据容器数量调整检查间隔
```bash
# 编辑配置文件
nano production_flow.env

# 修改检查间隔
FLOW_DAEMON_INTERVAL=300  # 5分钟（推荐）

# 重启服务
systemctl restart lxd-flow-continuity.service
```

### 2. 设置日志轮转
```bash
# 检查日志轮转配置
cat /etc/logrotate.d/lxd-flow-management

# 手动测试轮转
logrotate -f /etc/logrotate.d/lxd-flow-management
```

### 3. 监控设置
```bash
# 添加监控到系统监控中
echo "0 */6 * * * /usr/local/bin/lxd-flow-monitor >> /var/log/lxd-flow-monitor.log 2>&1" | crontab -
```

## ✅ 升级完成检查清单

- [ ] Python依赖安装成功
- [ ] 所有新文件已下载
- [ ] 数据库升级成功
- [ ] 现有容器已注册
- [ ] 守护进程运行正常
- [ ] 定时任务设置成功
- [ ] API接口测试通过
- [ ] 日志轮转配置完成
- [ ] 监控脚本运行正常
- [ ] 原有功能正常工作

## 🔄 回滚方案

如果升级出现问题，可以按以下步骤回滚：

```bash
# 1. 停止新服务
systemctl stop lxd-flow-continuity.service
systemctl disable lxd-flow-continuity.service

# 2. 恢复备份文件
cp lxc_manager.py.backup.* lxc_manager.py
cp app.py.backup.* app.py
cp app.ini.backup.* app.ini

# 3. 删除新增文件
rm -f flow_*.py flow_*.db create_flow_*.sql upgrade_*.py production_*.py test_*.py

# 4. 清理定时任务
crontab -l | grep -v flow_reset | crontab -

# 5. 重启原服务
systemctl restart your-lxd-service
```

升级完成后，您的系统将具备完整的流量重置功能，包括每月自动重置、手动重置、重启后流量连续性等特性！
